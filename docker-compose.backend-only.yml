version: '3.8'

services:
  # 只部署后端服务到Docker
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: homepage-backend
    restart: unless-stopped
    environment:
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
      - PORT=5000
    volumes:
      - ./backend/backups:/app/backups
      - ./frontend/public:/app/frontend/public
    ports:
      - "127.0.0.1:5000:5000"  # 只绑定到本地，通过Nginx代理
    networks:
      - homepage-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  homepage-network:
    driver: bridge

volumes:
  backend-data:
    driver: local

#!/bin/bash
# 生成多种格式的favicon图标

echo "🎨 生成favicon图标..."

# 检查是否安装了ImageMagick
if ! command -v convert &> /dev/null; then
    echo "❌ ImageMagick未安装"
    echo "Ubuntu/Debian: sudo apt install imagemagick"
    echo "CentOS/RHEL: sudo yum install ImageMagick"
    echo "macOS: brew install imagemagick"
    exit 1
fi

# 进入前端目录
cd frontend

# 从SVG生成不同尺寸的PNG图标
echo "📐 生成PNG图标..."

# 16x16 favicon
convert -background transparent favicon.svg -resize 16x16 favicon-16x16.png

# 32x32 favicon
convert -background transparent favicon.svg -resize 32x32 favicon-32x32.png

# 180x180 Apple touch icon
convert -background transparent favicon.svg -resize 180x180 apple-touch-icon.png

# 传统的favicon.ico (包含多个尺寸)
convert -background transparent favicon.svg -resize 16x16 favicon-16.png
convert -background transparent favicon.svg -resize 32x32 favicon-32.png
convert -background transparent favicon.svg -resize 48x48 favicon-48.png

# 合并为ico文件
convert favicon-16.png favicon-32.png favicon-48.png favicon.ico

# 清理临时文件
rm favicon-16.png favicon-32.png favicon-48.png

echo "✅ Favicon生成完成！"
echo "📁 生成的文件："
echo "   - favicon.svg (矢量图标)"
echo "   - favicon.ico (传统图标)"
echo "   - favicon-16x16.png"
echo "   - favicon-32x32.png" 
echo "   - apple-touch-icon.png"

echo ""
echo "🌐 HTML中已添加以下引用："
echo '   <link rel="icon" type="image/svg+xml" href="favicon.svg">'
echo '   <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">'
echo '   <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">'
echo '   <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">'

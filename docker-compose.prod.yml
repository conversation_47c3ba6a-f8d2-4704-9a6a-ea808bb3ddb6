version: '3.8'

services:
  # 后端服务 - 生产环境
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: homepage-backend-prod
    restart: always
    environment:
      - FLASK_ENV=production
      - PYTHONUNBUFFERED=1
    volumes:
      - ./backend/backups:/app/backups
      - ./frontend/public:/app/frontend/public
      - backend-logs:/app/logs
    networks:
      - homepage-network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端服务 (Nginx) - 生产环境
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile
    container_name: homepage-frontend-prod
    restart: always
    ports:
      - "80:80"
      - "443:443"  # 为HTTPS预留
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - homepage-network
    volumes:
      - ./ssl:/etc/nginx/ssl:ro  # SSL证书目录
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.5'
        reservations:
          memory: 64M
          cpus: '0.25'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  homepage-network:
    driver: bridge

volumes:
  backend-logs:
    driver: local
  backend-data:
    driver: local

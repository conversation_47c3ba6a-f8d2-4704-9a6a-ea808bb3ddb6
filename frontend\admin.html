<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LongDz - 配置管理系统</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        /* 登录页面样式 */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f5f5f5;
        }

        .login-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-header p {
            color: #666;
            font-size: 14px;
        }

        /* 管理系统布局 */
        .admin-layout {
            display: none;
            min-height: 100vh;
            background: #f5f5f5;
        }

        .admin-header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .admin-title {
            font-size: 20px;
            font-weight: 600;
            color: #111827;
        }

        .admin-user {
            color: #6b7280;
            font-size: 14px;
        }

        .admin-content {
            display: flex;
            min-height: calc(100vh - 64px);
        }

        .sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e5e7eb;
            padding: 24px 0;
        }

        .sidebar-nav {
            list-style: none;
        }

        .sidebar-nav li {
            margin-bottom: 4px;
        }

        .sidebar-nav a {
            display: block;
            padding: 12px 24px;
            color: #6b7280;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .sidebar-nav a:hover,
        .sidebar-nav a.active {
            background: #f3f4f6;
            color: #4f46e5;
        }

        .main-content {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 24px;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 8px;
        }

        .page-description {
            color: #6b7280;
            font-size: 14px;
        }

        /* 表单和卡片样式 */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s, box-shadow 0.2s;
        }

        .form-control:focus {
            outline: none;
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .form-text {
            display: block;
            margin-top: 6px;
            font-size: 12px;
            color: #6b7280;
            line-height: 1.4;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #ef4444;
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-block {
            width: 100%;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .button-group .btn {
            flex: 0 0 auto;
        }

        /* Input Group 样式 */
        .input-group {
            display: flex;
            width: 100%;
        }

        .input-group .form-control {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-right: none;
        }

        .input-group .btn {
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            padding: 12px 16px;
            white-space: nowrap;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 24px;
        }

        .card-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #111827;
        }

        .card-body {
            padding: 24px;
        }

        .grid {
            display: grid;
            gap: 20px;
        }

        .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr);
        }

        .item-card {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .item-title {
            font-weight: 600;
            color: #111827;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .error-message {
            color: #ef4444;
            font-size: 12px;
            margin-top: 4px;
        }

        /* 管理列表容器样式 */
        .management-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            background: #f9fafb;
            margin-top: 16px;
        }

        .management-container::-webkit-scrollbar {
            width: 8px;
        }

        .management-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .management-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .management-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 背景图片预设样式 */
        .preset-controls {
            display: flex;
            align-items: center;
            gap: 8px;
            margin: 12px 0;
        }

        .presets-container {
            flex: 1;
            overflow: hidden;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .background-presets {
            display: flex;
            gap: 12px;
            padding: 12px;
            transition: transform 0.3s ease;
        }

        .background-presets.scrollable {
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .background-presets.scrollable::-webkit-scrollbar {
            display: none;
        }

        .preset-actions {
            margin-top: 8px;
        }

        .preset-item {
            position: relative;
            width: 120px;
            height: 80px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s;
            flex-shrink: 0;
        }

        .preset-item:hover {
            border-color: #4f46e5;
            transform: scale(1.05);
        }

        .preset-item.selected {
            border-color: #4f46e5;
            box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
        }

        .preset-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preset-item .preset-label {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            font-size: 12px;
            text-align: center;
            cursor: text;
        }

        .preset-item .preset-label input {
            background: transparent;
            border: none;
            color: white;
            width: 100%;
            text-align: center;
            font-size: 12px;
            outline: none;
        }

        .preset-item .preset-delete {
            position: absolute;
            top: 4px;
            right: 4px;
            width: 20px;
            height: 20px;
            background: rgba(239, 68, 68, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            font-size: 12px;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            z-index: 10;
        }

        .preset-item .preset-delete:hover {
            background: rgba(239, 68, 68, 1);
            transform: scale(1.1);
        }

        .preset-item:hover .preset-delete {
            display: flex;
        }

        /* 背景预览样式 */
        .background-preview {
            width: 200px;
            height: 120px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
        }

        .background-preview img,
        .background-preview video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preview-video {
            background: #000;
        }

        .preview-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
            color: #6b7280;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .admin-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .grid-cols-2 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <!-- 登录页面 -->
    <div class="login-container" id="loginContainer">
        <div class="login-card">
            <div class="login-header">
                <h1>配置管理系统</h1>
                <p>请输入管理密码以继续</p>
            </div>
            <form onsubmit="return false;">
                <div class="form-group">
                    <label for="password">密码</label>
                    <input type="password" id="password" class="form-control" placeholder="请输入密码" />
                    <div id="loginError" class="error-message"></div>
                </div>
                <button type="button" class="btn btn-primary btn-block" onclick="login()">登录</button>
            </form>
        </div>
    </div>

    <!-- 管理系统主界面 -->
    <div class="admin-layout" id="adminLayout">
        <header class="admin-header">
            <div class="admin-title">配置管理系统</div>
            <div class="admin-user">管理员已登录</div>
        </header>

        <div class="admin-content">
            <aside class="sidebar">
                <nav>
                    <ul class="sidebar-nav">
                        <li><a href="#" class="nav-link active" data-tab="profile">个人信息</a></li>
                        <li><a href="#" class="nav-link" data-tab="skills">技能管理</a></li>
                        <li><a href="#" class="nav-link" data-tab="projects">项目管理</a></li>
                        <li><a href="#" class="nav-link" data-tab="friends">友链管理</a></li>
                        <li><a href="#" class="nav-link" data-tab="social">社交媒体</a></li>
                        <li><a href="#" class="nav-link" data-tab="music">音乐配置</a></li>
                        <li><a href="#" class="nav-link" data-tab="background">背景设置</a></li>
                    </ul>
                </nav>
            </aside>

            <main class="main-content">
                <!-- 个人信息 -->
                <div class="tab-pane active" id="profile">
                    <div class="page-header">
                        <h1 class="page-title">个人信息</h1>
                        <p class="page-description">管理个人基本信息和简介</p>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">基本信息</h3>
                        </div>
                        <div class="card-body">
                            <div class="grid grid-cols-2">
                                <div class="form-group">
                                    <label>姓名</label>
                                    <input type="text" id="profileName" class="form-control" />
                                </div>
                                <div class="form-group">
                                    <label>职业</label>
                                    <input type="text" id="profileTitle" class="form-control" />
                                </div>
                                <div class="form-group">
                                    <label>生日</label>
                                    <input type="date" id="profileBirthday" class="form-control" />
                                </div>
                                <div class="form-group">
                                    <label>ID</label>
                                    <input type="text" id="profileId" class="form-control" />
                                </div>
                                <div class="form-group">
                                    <label>网站开始时间</label>
                                    <input type="date" id="profileSiteStart" class="form-control" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label>自我介绍</label>
                                <textarea id="profileDescription" class="form-control" rows="4"></textarea>
                            </div>
                            <div class="form-group">
                                <label>头像URL</label>
                                <input type="url" id="profileAvatar" class="form-control" />
                            </div>
                            <button class="btn btn-success" onclick="saveProfile()">保存个人信息</button>
                        </div>
                    </div>
                </div>

                <!-- 其他标签页内容 -->
                <div class="tab-pane" id="skills">
                    <div class="page-header">
                        <h1 class="page-title">技能管理</h1>
                        <p class="page-description">管理技能列表和熟练程度</p>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">技能列表</h3>
                        </div>
                        <div class="card-body">
                            <div class="button-group">
                                <button class="btn btn-primary" onclick="addSkill()">添加技能</button>
                                <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                            </div>
                            <div class="management-container">
                                <div id="skillsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="projects">
                    <div class="page-header">
                        <h1 class="page-title">项目管理</h1>
                        <p class="page-description">管理项目展示列表</p>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">项目列表</h3>
                        </div>
                        <div class="card-body">
                            <div class="button-group">
                                <button class="btn btn-primary" onclick="addProject()">添加项目</button>
                                <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                            </div>
                            <div class="management-container">
                                <div id="projectsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="friends">
                    <div class="page-header">
                        <h1 class="page-title">友链管理</h1>
                        <p class="page-description">管理友情链接</p>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">友链列表</h3>
                        </div>
                        <div class="card-body">
                            <div class="button-group">
                                <button class="btn btn-primary" onclick="addFriend()">添加友链</button>
                                <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                            </div>
                            <div class="management-container">
                                <div id="friendsList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="social">
                    <div class="page-header">
                        <h1 class="page-title">社交媒体</h1>
                        <p class="page-description">管理社交媒体链接</p>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">社交媒体列表</h3>
                        </div>
                        <div class="card-body">
                            <div class="button-group">
                                <button class="btn btn-primary" onclick="addSocial()">添加社交媒体</button>
                                <button class="btn btn-success" onclick="saveConfig()">保存配置</button>
                            </div>
                            <div class="management-container">
                                <div id="socialList"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="music">
                    <div class="page-header">
                        <h1 class="page-title">音乐配置</h1>
                        <p class="page-description">配置音乐播放器</p>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">音乐设置</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label>当前歌曲ID</label>
                                <input type="text" id="currentSongId" class="form-control" />
                            </div>
                            <div class="form-group">
                                <label>播放列表 (每行一个ID)</label>
                                <textarea id="playlist" class="form-control" rows="6"
                                    placeholder="输入歌曲ID，每行一个"></textarea>
                            </div>
                            <button class="btn btn-success" onclick="saveMusic()">保存音乐配置</button>
                        </div>
                    </div>
                </div>

                <div class="tab-pane" id="background">
                    <div class="page-header">
                        <h1 class="page-title">背景设置</h1>
                        <p class="page-description">配置页面背景图片</p>
                    </div>

                    <!-- 个人资料背景配置 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">个人资料背景配置</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="showBackgroundImage" style="margin-right: 8px;">
                                    显示个人资料背景图片
                                </label>
                                <small class="form-text">控制是否在左侧斜分割线区域显示背景图片</small>
                            </div>

                            <div class="form-group">
                                <label>
                                    <input type="checkbox" id="enableGlassMorphism" style="margin-right: 8px;">
                                    启用毛玻璃效果
                                </label>
                                <small class="form-text">控制左侧斜分割线区域的毛玻璃模糊效果</small>
                            </div>

                            <!-- 背景图片预设列表 -->
                            <div class="form-group">
                                <label>背景图片预设</label>
                                <div class="preset-controls">
                                    <button type="button" class="btn btn-secondary btn-sm"
                                        onclick="scrollPresets('profilePresets', -1)">‹</button>
                                    <div class="presets-container">
                                        <div id="profilePresets" class="background-presets scrollable">
                                            <!-- 预设背景图片将在这里动态生成 -->
                                        </div>
                                    </div>
                                    <button type="button" class="btn btn-secondary btn-sm"
                                        onclick="scrollPresets('profilePresets', 1)">›</button>
                                </div>
                                <div class="preset-actions">
                                    <button type="button" class="btn btn-primary btn-sm"
                                        onclick="addCustomPreset('profile')">添加自定义预设</button>
                                </div>
                                <small class="form-text">点击选择预设的背景图片，双击可重命名</small>
                            </div>

                            <div class="form-group">
                                <label>个人资料背景图片URL</label>
                                <input type="url" id="profileImage" class="form-control" placeholder="输入个人资料背景图片URL" />
                                <small class="form-text">用于左侧斜分割线区域的背景图片</small>
                            </div>

                            <!-- 当前背景预览 -->
                            <div class="form-group">
                                <label>当前背景预览</label>
                                <div id="profileBackgroundPreview" class="background-preview">
                                    <img id="profilePreviewImage" src="" alt="背景预览" style="display: none;">
                                    <div id="profilePreviewPlaceholder" class="preview-placeholder">暂无背景图片</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧背景配置 -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">右侧背景配置</h3>
                        </div>
                        <div class="card-body">
                            <div class="form-group">
                                <label>背景类型</label>
                                <select id="backgroundType" class="form-control" onchange="toggleBackgroundType()">
                                    <option value="image">图片背景</option>
                                    <option value="video">视频背景</option>
                                </select>
                                <small class="form-text">选择右侧背景的类型</small>
                            </div>

                            <div class="form-group" id="imageBackgroundGroup">
                                <!-- 右侧背景图片预设 -->
                                <div class="form-group">
                                    <label>右侧背景图片预设</label>
                                    <div class="preset-controls">
                                        <button type="button" class="btn btn-secondary btn-sm"
                                            onclick="scrollPresets('rightPresets', -1)">‹</button>
                                        <div class="presets-container">
                                            <div id="rightPresets" class="background-presets scrollable">
                                                <!-- 预设背景图片将在这里动态生成 -->
                                            </div>
                                        </div>
                                        <button type="button" class="btn btn-secondary btn-sm"
                                            onclick="scrollPresets('rightPresets', 1)">›</button>
                                    </div>
                                    <div class="preset-actions">
                                        <button type="button" class="btn btn-primary btn-sm"
                                            onclick="addCustomPreset('right')">添加自定义预设</button>
                                    </div>
                                    <small class="form-text">点击选择预设的背景图片，双击可重命名</small>
                                </div>

                                <label>右侧背景图片URL</label>
                                <input type="url" id="backgroundImage" class="form-control"
                                    placeholder="输入右侧内容区域的背景图片URL" />
                                <small class="form-text">用于右侧内容区域的背景图片</small>

                                <!-- 右侧背景预览 -->
                                <div class="form-group">
                                    <label>右侧背景预览</label>
                                    <div id="rightBackgroundPreview" class="background-preview">
                                        <img id="rightPreviewImage" src="" alt="背景预览" style="display: none;">
                                        <div id="rightPreviewPlaceholder" class="preview-placeholder">暂无背景图片</div>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group" id="videoBackgroundGroup" style="display: none;">
                                <!-- 视频背景预设 -->
                                <div class="form-group">
                                    <label>视频背景预设</label>
                                    <div class="preset-controls">
                                        <button type="button" class="btn btn-secondary btn-sm"
                                            onclick="scrollPresets('videoPresets', -1)">‹</button>
                                        <button type="button" class="btn btn-primary btn-sm"
                                            onclick="addCustomPreset('video')">添加自定义</button>
                                        <button type="button" class="btn btn-secondary btn-sm"
                                            onclick="scrollPresets('videoPresets', 1)">›</button>
                                    </div>
                                    <div class="preset-container">
                                        <div id="videoPresets" class="background-presets"></div>
                                    </div>
                                </div>

                                <label>右侧背景视频URL</label>
                                <div class="input-preview-container">
                                    <input type="url" id="backgroundVideo" class="form-control"
                                        placeholder="输入右侧内容区域的背景视频URL" oninput="updateVideoPreview()" />
                                    <div class="background-preview">
                                        <video id="videoPreview" class="preview-video" muted loop
                                            style="display: none;">
                                            您的浏览器不支持视频播放
                                        </video>
                                        <div id="videoPreviewPlaceholder" class="preview-placeholder">暂无背景视频</div>
                                    </div>
                                </div>
                                <small class="form-text">支持MP4格式视频，建议使用较小的文件以提高加载速度</small>
                            </div>
                        </div>
                    </div>

                    <div class="button-group">
                        <button class="btn btn-success" onclick="saveBackground()">保存背景设置</button>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="src/admin.js"></script>
</body>

</html>
<!DOCTYPE html>
<html>
<head>
    <title>生成Favicon</title>
</head>
<body>
    <h2>简单的Favicon生成器</h2>
    <p>如果你没有ImageMagick，可以使用这个简单的方法：</p>
    
    <canvas id="favicon" width="32" height="32" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="downloadFavicon()">下载 favicon.ico</button>
    
    <script>
        // 绘制简单的favicon
        const canvas = document.getElementById('favicon');
        const ctx = canvas.getContext('2d');
        
        // 创建渐变背景
        const gradient = ctx.createLinearGradient(0, 0, 32, 32);
        gradient.addColorStop(0, '#667eea');
        gradient.addColorStop(1, '#764ba2');
        
        // 绘制圆形背景
        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(16, 16, 15, 0, 2 * Math.PI);
        ctx.fill();
        
        // 绘制白色边框
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1;
        ctx.stroke();
        
        // 绘制字母L
        ctx.fillStyle = '#fff';
        ctx.font = 'bold 18px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('L', 16, 16);
        
        function downloadFavicon() {
            // 创建一个临时链接来下载
            const link = document.createElement('a');
            link.download = 'favicon.png';
            link.href = canvas.toDataURL();
            link.click();
            
            alert('PNG文件已下载！\n\n要转换为ICO格式，你可以：\n1. 使用在线转换工具\n2. 安装ImageMagick后运行 generate-favicons.sh\n3. 直接使用PNG文件（现代浏览器支持）');
        }
    </script>
    
    <h3>使用说明：</h3>
    <ol>
        <li>点击"下载 favicon.ico"按钮</li>
        <li>将下载的PNG文件重命名为favicon.ico</li>
        <li>放到frontend目录中</li>
        <li>或者使用在线工具转换为真正的ICO格式</li>
    </ol>
    
    <h3>推荐的在线ICO转换工具：</h3>
    <ul>
        <li><a href="https://www.favicon.cc/" target="_blank">favicon.cc</a></li>
        <li><a href="https://realfavicongenerator.net/" target="_blank">Real Favicon Generator</a></li>
        <li><a href="https://www.favicon-generator.org/" target="_blank">Favicon Generator</a></li>
    </ul>
</body>
</html>

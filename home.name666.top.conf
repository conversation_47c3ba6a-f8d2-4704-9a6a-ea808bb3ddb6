# 完整的Nginx站点配置 - 替换你的 /etc/nginx/sites-available/home.name666.top

server {
    listen 80;
    server_name home.name666.top;
    
    # 如果有SSL证书，重定向到HTTPS
    # return 301 https://$server_name$request_uri;
    
    # 网站根目录 - 请修改为你的实际路径
    root /home/<USER>/homepage/frontend;  # 修改这个路径！
    index index.html;

    # 访问和错误日志
    access_log /var/log/nginx/home.name666.top.access.log;
    error_log /var/log/nginx/home.name666.top.error.log;

    # 基础性能配置
    client_max_body_size 16M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    
    # Gzip压缩 - 提升传输效率
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        font/truetype
        font/opentype
        application/font-woff
        application/font-woff2;

    # 静态资源缓存 - 大幅提升性能
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|otf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # 字体文件跨域支持
        location ~* \.(woff|woff2|ttf|eot|otf)$ {
            add_header Access-Control-Allow-Origin "*";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # Favicon处理
    location = /favicon.ico {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
        log_not_found off;
    }

    # 前端静态文件 - SPA支持
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    }

    # API代理到Python后端 - 核心配置
    location /api/ {
        # 代理到本地Python服务
        proxy_pass http://127.0.0.1:3001/;
        
        # 请求头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲配置 - 提升性能
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # 错误处理和故障转移
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 30s;
        
        # 禁用缓存API响应
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }

    # 管理后台页面
    location = /admin.html {
        try_files $uri =404;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        
        # 可选：IP访问限制
        # allow ***********/24;
        # deny all;
    }

    # 健康检查端点
    location = /health {
        access_log off;
        return 200 "nginx healthy\n";
        add_header Content-Type text/plain;
    }

    # 后端健康检查代理
    location = /api-health {
        access_log off;
        proxy_pass http://127.0.0.1:3001/api/health;
        proxy_connect_timeout 5s;
        proxy_read_timeout 5s;
    }

    # 安全配置 - 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf|bak|backup)$ {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 禁止访问备份目录
    location ~ ^/(backups|logs|tmp)/ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTPS配置（如果有SSL证书）
# server {
#     listen 443 ssl http2;
#     server_name home.name666.top;
#     
#     # SSL证书配置 - 请替换为你的证书路径
#     ssl_certificate /etc/ssl/certs/home.name666.top.crt;
#     ssl_certificate_key /etc/ssl/private/home.name666.top.key;
#     
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     ssl_stapling on;
#     ssl_stapling_verify on;
#     
#     # HSTS安全头
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#     
#     # 其他配置与HTTP相同，复制上面的location块
#     # ...
# }

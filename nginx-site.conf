# Nginx站点配置 - 适用于 /etc/nginx/sites-available/home.name666.top
# 前端静态文件 + 后端Docker代理

server {
    listen 80;
    server_name home.name666.top;
    
    # 网站根目录 - 指向你的前端静态文件
    root /path/to/your/homepage/frontend;
    index index.html;

    # 访问日志
    access_log /var/log/nginx/home.name666.top.access.log;
    error_log /var/log/nginx/home.name666.top.error.log;

    # 性能优化
    client_max_body_size 16M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # 静态资源缓存优化
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        
        # 跨域支持（如果需要）
        add_header Access-Control-Allow-Origin "*";
    }

    # 前端静态文件
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache";
        
        # 安全头
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
    }

    # API代理到Docker后端
    location /api/ {
        proxy_pass http://127.0.0.1:5000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
    }

    # 健康检查端点
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # 管理后台
    location /admin.html {
        try_files $uri =404;
        add_header Cache-Control "no-cache";
    }

    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(htaccess|htpasswd|ini|log|sh|sql|conf)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTPS配置（如果有SSL证书）
server {
    listen 443 ssl http2;
    server_name home.name666.top;
    
    # SSL证书配置
    # ssl_certificate /path/to/your/cert.pem;
    # ssl_certificate_key /path/to/your/private.key;
    
    # SSL优化配置
    # ssl_protocols TLSv1.2 TLSv1.3;
    # ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    # ssl_prefer_server_ciphers off;
    # ssl_session_cache shared:SSL:10m;
    # ssl_session_timeout 10m;
    
    # 其他配置与HTTP相同...
    # 可以复制上面的location块
}

#!/bin/bash
# Docker部署脚本 - 一键部署个人主页

set -e

echo "🚀 开始部署个人主页..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 停止并删除旧容器
echo "🛑 停止旧容器..."
docker-compose down --remove-orphans

# 清理旧镜像（可选）
read -p "是否清理旧镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建并启动服务
echo "🔨 构建镜像..."
docker-compose build --no-cache

echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示日志
echo "📋 显示最近日志..."
docker-compose logs --tail=20

echo "✅ 部署完成！"
echo "🌐 访问地址: http://localhost"
echo "📊 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"

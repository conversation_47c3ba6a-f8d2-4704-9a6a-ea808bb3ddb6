# Docker 部署指南

## 🚀 快速部署

### 开发环境
```bash
# 一键部署
chmod +x deploy.sh
./deploy.sh

# 或手动部署
docker-compose up -d
```

### 生产环境
```bash
# 使用生产配置
docker-compose -f docker-compose.prod.yml up -d
```

## 📁 文件结构

```
├── backend/
│   ├── Dockerfile              # 后端Docker配置
│   ├── requirements.txt        # Python依赖
│   └── app.py                 # Flask应用
├── frontend/
│   ├── Dockerfile             # 前端Docker配置
│   └── ...                    # 静态文件
├── nginx.conf                 # Nginx配置
├── docker-compose.yml         # 开发环境配置
├── docker-compose.prod.yml    # 生产环境配置
├── deploy.sh                  # 部署脚本
└── .dockerignore             # Docker忽略文件
```

## ⚡ 性能优化特性

### 后端优化
- 使用 `python:3.11-alpine` 轻量级镜像
- Gunicorn WSGI服务器，支持多进程/线程
- 非root用户运行，提升安全性
- 资源限制：CPU 0.5核，内存256M

### 前端优化
- Nginx静态文件服务
- Gzip压缩减少传输大小
- 静态资源缓存1年
- 资源限制：CPU 0.25核，内存64M

### 网络优化
- 内部网络通信
- 健康检查确保服务可用
- 反向代理到后端API

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 清理资源
docker-compose down -v
docker system prune -f
```

## 🌐 访问地址

- **主页**: http://localhost
- **管理后台**: http://localhost/admin.html
- **API文档**: http://localhost/api/health

## 📊 监控

### 健康检查
- 后端：`/api/health`
- 前端：`/health`

### 日志查看
```bash
# 后端日志
docker-compose logs backend

# 前端日志
docker-compose logs frontend

# 实时日志
docker-compose logs -f
```

## 🔒 生产环境建议

1. **SSL证书**: 将证书放在 `./ssl/` 目录
2. **环境变量**: 使用 `.env` 文件管理敏感信息
3. **备份**: 定期备份 `./backend/backups/` 目录
4. **监控**: 配置日志收集和监控系统
5. **防火墙**: 只开放必要端口（80, 443）

## 🛠️ 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :80
   
   # 修改端口
   # 编辑 docker-compose.yml 中的 ports 配置
   ```

2. **权限问题**
   ```bash
   # 检查文件权限
   ls -la backend/
   
   # 修复权限
   chmod +x deploy.sh
   ```

3. **内存不足**
   ```bash
   # 检查资源使用
   docker stats
   
   # 调整资源限制
   # 编辑 docker-compose.yml 中的 deploy.resources
   ```

## 📈 扩展部署

### 多实例部署
```bash
# 扩展后端实例
docker-compose up -d --scale backend=3
```

### 负载均衡
在 `nginx.conf` 中配置多个后端实例：
```nginx
upstream backend {
    server backend_1:5000;
    server backend_2:5000;
    server backend_3:5000;
}
```
